import React, { useState, memo, useMemo } from 'react';
import { useLanguage } from '@/hooks/use-language';
import ScrollReveal from './ScrollReveal';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

import PricingOrbitingIcons from './PricingOrbitingIcons';


// Define interface for the pricing plan
interface PricingPlan {
  name: string;
  tag: string;
  description: string;
  price: string;
  features: string[];
  cta: string;
  popular: boolean;
  bestDeal: boolean;
}

// Memoized pricing card component for better performance
const PricingCard = memo(({ plan, index }: { plan: PricingPlan; index: number }) => (
  <ScrollReveal key={plan.name} delay={index * 150}>
    {/* Outer container that isolates tag positioning from card transforms */}
    <div className="relative">
      {/* Static tag positioned with fixed context - isolated from card animations */}
      {plan.tag && (
        <div
          className={cn(
            "pricing-tag-static px-6 py-2 rounded-full text-sm font-bold shadow-lg premium-glass z-30",
            plan.popular ? "text-premium-gold border border-premium-gold/30" :
            plan.bestDeal ? "text-neon-purple border border-neon-purple/30" :
            "text-neon-cyan border border-neon-cyan/30"
          )}
        >
          {plan.tag}
        </div>
      )}

      {/* Card container with isolated transform context */}
      <div className={plan.tag ? "pricing-card-container-with-tag" : "pricing-card-container"}>
        {/* Animated card container - transforms isolated from tag */}
        <motion.div
          className={cn(
            "flex flex-col h-full rounded-2xl p-4 sm:p-6 md:p-8 luxury-card transition-all duration-500 ease-out relative group",
            plan.popular
              ? "border-2 border-premium-gold/40 hover:border-premium-gold/60 hover:shadow-[0_0_40px_rgba(255,215,0,0.4)]"
              : plan.bestDeal
              ? "border-2 border-neon-purple/40 hover:border-neon-purple/60 hover:shadow-[0_0_40px_rgba(179,102,255,0.4)]"
              : "border border-neon-cyan/30 hover:border-neon-cyan/50 hover:shadow-neon-glow"
          )}
          whileHover={{ y: -8, scale: 1.02 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          style={{ willChange: 'transform' }}
        >
          {/* Premium glow effects */}
          <div className="absolute -inset-2 rounded-3xl bg-gradient-aurora opacity-0 group-hover:opacity-20 blur-xl z-0 transition-opacity duration-500"></div>
          <div className="absolute -inset-1 rounded-2xl bg-gradient-luxury opacity-0 group-hover:opacity-10 blur-lg z-0 transition-opacity duration-500"></div>
          <div className="pricing-card-glow group-hover:opacity-100"></div>

          <div className="relative flex-grow flex flex-col z-10">
            <h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 text-center pt-4 sm:pt-6 text-gradient-aurora">{plan.name}</h3>
            <p className="text-foreground/80 text-sm sm:text-base mb-6 sm:mb-8 text-center min-h-[60px] sm:min-h-[80px] leading-relaxed">{plan.description}</p>

            <div className="mb-6 sm:mb-8 text-center">
              <span className="text-4xl sm:text-5xl font-bold text-gradient-premium drop-shadow-glow">{plan.price}</span>
            </div>
            <ul className="space-y-3 sm:space-y-4 mb-8 sm:mb-10 flex-grow">
              {plan.features.map((feature: string) => (
                <li key={feature} className="flex items-start sm:items-center">
                  <Check className={cn("h-5 w-5 sm:h-6 sm:w-6 mr-3 sm:mr-4 flex-shrink-0 mt-0.5 sm:mt-0", "text-premium-gold drop-shadow-glow")} />
                  <span className="text-foreground/90 text-sm sm:text-base">{feature}</span>
                </li>
              ))}
            </ul>

            <Button
              className={cn(
                "w-full mt-auto font-bold py-3 sm:py-4 rounded-xl shadow-lg transition-all duration-300 text-base sm:text-lg min-h-[44px] touch-manipulation",
                plan.popular || plan.bestDeal
                  ? "premium-button"
                  : "bg-gradient-neon text-background hover:bg-gradient-to-r hover:from-neon-cyan hover:to-neon-purple hover:shadow-[0_8px_30px_rgba(0,229,229,0.5)]",
                "hover:scale-105 active:scale-95"
              )}
              type="button"
            >
              {plan.cta}
            </Button>

            {/* Floating premium accent elements */}
            <motion.div
              className="absolute -top-2 -right-2 w-4 h-4 bg-premium-gold rounded-full opacity-0 group-hover:opacity-80 z-20 transition-opacity duration-500"
              animate={{ rotate: 360, scale: [1, 1.2, 1] }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
            ></motion.div>
            <motion.div
              className="absolute -bottom-2 -left-2 w-3 h-3 bg-premium-silver rounded-full opacity-0 group-hover:opacity-60 z-20 transition-opacity duration-500"
              animate={{ rotate: -360, scale: [1, 1.1, 1] }}
              transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
            ></motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  </ScrollReveal>
));

PricingCard.displayName = 'PricingCard';

const PricingSection: React.FC = () => {
  const { t } = useLanguage();
  const [isDesignOnly, setIsDesignOnly] = useState(false);

  // Memoize plans data to prevent unnecessary re-renders
  const websitePlans = useMemo(() => [
    {
      name: "Monthly Website Plan",
      tag: "Starter",
      description: "Includes 20 product images and matching Facebook & Instagram marketing posters monthly. Ideal for lean businesses with ongoing design needs and low upfront costs.",
      price: "$200/month",
      features: [
        "Custom-built website",
        "20 product images monthly",
        "Matching Facebook & Instagram marketing posters monthly",
        "Low upfront costs",
        "Ongoing design needs coverage"
      ],
      cta: "Choose Plan",
      popular: false,
      bestDeal: false,
    },
    {
      name: "Yearly Website Plan",
      tag: "Popular",
      description: "Includes 50 product images and Facebook & Instagram marketing posters every month. Best value for long-term businesses looking to save big annually.",
      price: "$1,499/year",
      features: [
        "Custom-built website",
        "50 product images monthly",
        "Facebook & Instagram marketing posters monthly",
        "Best value annually",
        "Significant annual savings"
      ],
      cta: "Choose Plan",
      popular: true,
      bestDeal: false,
    },
    {
      name: "One-Time Website Plan",
      tag: "Best Deal",
      description: "Includes a custom-built site plus 50 product images and social media posters delivered monthly for 12 months. No recurring charges — full service in one payment.",
      price: "$2,500 (one-time)",
      features: [
        "Custom-built website",
        "50 product images monthly for 12 months",
        "Social media posters monthly for 12 months",
        "No recurring charges",
        "Full service in one payment"
      ],
      cta: "Contact Us",
      popular: false,
      bestDeal: true,
    },
  ], []);

  const designOnlyPlans = useMemo(() => [
    {
      name: "Design Starter",
      tag: "",
      description: "Ideal for quick promotions or small campaigns.",
      price: "$99",
      features: [
        "20 product images",
        "Facebook & Instagram posters"
      ],
      cta: "Choose Plan",
      popular: false,
      bestDeal: false,
    },
    {
      name: "Design Pro",
      tag: "Popular",
      description: "Great for ongoing e-commerce or brand promotion.",
      price: "$149",
      features: [
        "50 product images",
        "Social content",
        "Great for ongoing campaigns"
      ],
      cta: "Choose Plan",
      popular: true,
      bestDeal: false,
    },
    {
      name: "Design Max",
      tag: "Best Deal",
      description: "High-volume, high-impact design bundle for brands that want to scale fast.",
      price: "$250",
      features: [
        "100 product images",
        "Complete social media bundle",
        "High-volume design bundle",
        "Ideal for scaling fast"
      ],
      cta: "Choose Plan",
      popular: false,
      bestDeal: true,
    },
  ], []);

  const plansToDisplay = useMemo(() =>
    isDesignOnly ? designOnlyPlans : websitePlans,
    [isDesignOnly, designOnlyPlans, websitePlans]
  );

  // Create a toggle handler to ensure state updates properly
  const handleToggleChange = (checked: boolean) => {
    setIsDesignOnly(checked);
  };

  return (
    <section id="pricing" className="section-padding relative overflow-hidden">
      {/* Unified background layers for optimal readability and visual harmony */}
      <div className="absolute inset-0 bg-gradient-cosmic-dark"></div>
      <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
      <div className="absolute inset-0 bg-gradient-radial from-neon-purple/4 via-transparent to-neon-cyan/2"></div>
      <div className="absolute inset-0 bg-gradient-aurora opacity-1"></div>
      <div className="absolute inset-0 bg-black/40"></div>

      {/* Pricing-specific Orbital Icons System */}
      <PricingOrbitingIcons className="z-0" />



      {/* Enhanced gradient orb decorations optimized for darker background */}
      <div className="absolute top-20 right-1/4 w-48 h-48 bg-gradient-ocean opacity-20 rounded-full blur-3xl animate-pulse-glow z-5"></div>
      <div className="absolute bottom-20 left-1/4 w-56 h-56 bg-gradient-sunset opacity-18 rounded-full blur-3xl animate-pulse-glow z-5"></div>
      <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-aurora opacity-12 rounded-full blur-3xl animate-float z-5"></div>

      <div className="container max-w-7xl mx-auto relative z-20 px-4 sm:px-6">
        {/* Centered content with enhanced z-index */}
        <div className="flex flex-col items-center">
          <ScrollReveal delay={300}>
            <div className="inline-block mb-4 sm:mb-6 px-4 sm:px-8 py-2 sm:py-3 rounded-full premium-glass text-sm sm:text-base font-medium">
              <span className="text-gradient-aurora">💎 Premium Pricing</span>
            </div>
          </ScrollReveal>

          <ScrollReveal delay={500}>
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-center mb-4 sm:mb-6 md:mb-8 leading-tight max-w-4xl" style={{fontWeight: '950', WebkitTextStroke: '0.5px currentColor'}}>
              <span className="text-gradient-aurora drop-shadow-glow">{t('pricing.title')}</span>
            </h2>
          </ScrollReveal>

          <ScrollReveal delay={700}>
            <p className="text-lg sm:text-xl md:text-2xl text-center text-foreground/80 mb-8 sm:mb-12 max-w-3xl leading-relaxed">
              {t('pricing.subtitle')}
            </p>
          </ScrollReveal>
        </div>

        <ScrollReveal delay={900}>
          {/* Enhanced premium switch container - Improved for mobile */}
          <div className="flex items-center justify-center py-2 sm:py-3 px-4 sm:px-6 rounded-full premium-glass mx-auto max-w-sm mb-8 sm:mb-12">
            <span
              className={cn(
                "text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2 transition-all duration-300 font-medium min-h-[44px] flex items-center",
                !isDesignOnly ? "text-premium-gold" : "text-foreground/60"
              )}
            >
              Websites
            </span>
            <Switch
              checked={isDesignOnly}
              onCheckedChange={handleToggleChange}
              className={cn(
                "data-[state=checked]:bg-premium-gold data-[state=unchecked]:bg-neon-cyan/30",
                "transition-all duration-300 mx-2 sm:mx-4 scale-90 sm:scale-100 touch-manipulation"
              )}
            />
            <span
              className={cn(
                "text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2 transition-all duration-300 font-medium min-h-[44px] flex items-center",
                isDesignOnly ? "text-premium-gold" : "text-foreground/60"
              )}
            >
              Design Only
            </span>
          </div>
        </ScrollReveal>

        {/* Enhanced pricing cards grid with premium styling and performance optimization - Mobile optimized */}
        <div
          key={isDesignOnly ? 'design' : 'websites'}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 opacity-100 transition-opacity duration-300"
        >
          {plansToDisplay.map((plan, index) => (
            <PricingCard key={plan.name} plan={plan} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default memo(PricingSection);
