import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowUpRight } from 'lucide-react';
import ScrollReveal from './ScrollReveal';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import OptimizedImage from '@/components/OptimizedImage';

interface PortfolioItemType {
  id: string;
  title: string;
  category: string;
  location?: string;
  imageUrl: string;
  liveUrl: string;
  description: string;
}

const portfolioItemsData: PortfolioItemType[] = [
  // Updated website showcase items with new images as requested
  { id: 'benfresh', title: 'BenFresh', category: 'E-commerce Website', imageUrl: '/websites/benfresh.jpg', liveUrl: 'https://www.benfresh.de/', description: 'E-commerce platform with clean, user-friendly interface offering a seamless shopping experience with optimized product displays and secure checkout process.' },
  { id: 'rein-glanz-service', title: 'Rein Glanz Service GmbH', category: 'Cleaning Services Website', imageUrl: '/websites/ablass.jpg', liveUrl: 'https://rein-glanz-service-gmbh.lovable.app/', description: 'Professional cleaning service website with modern design, showcasing commercial and residential cleaning services with easy booking and transparent pricing.' },

  // Existing portfolio items
  { id: 'aurora-dental-clinic', title: 'Aurora Dental Clinic', category: 'Healthcare Website', imageUrl: '/websites/aurora-dental-clinic-min.png', liveUrl: 'https://www.auroradentalclinic.co.uk/', description: 'Award-winning private dental clinics in Wiltshire offering top-tier cosmetic and restorative dental treatments with a patient-focused approach.' },
  { id: 'securitas-security', title: 'Securitas Global Security', category: 'Security Services Website', imageUrl: '/websites/securitas-security-min.png', liveUrl: 'https://www.securitas.com/', description: 'Integrated guarding services backed by world-class technology solutions providing comprehensive security for businesses and organizations worldwide.' },
  { id: 'hogan-lovells-germany', title: 'Hogan Lovells – Germany', category: 'International Law Firm Website', imageUrl: '/websites/hogan-lovells-germany-min.png', liveUrl: 'https://www.hoganlovells.com/en/locations/germany', description: 'Legal and financial insight across German and international markets offering specialized expertise in corporate, intellectual property, and regulatory law.' },
  { id: 'cleanwhale-berlin', title: 'CleanWhale Berlin', category: 'Home Services Website', imageUrl: '/websites/cleanwhale-berlin-min.png', liveUrl: 'https://cleanwhale.de/', description: 'Smart booking and transparent pricing for premium home cleaning services in Berlin with eco-friendly cleaning solutions and professional staff.' },
  { id: 'superlist-productivity', title: 'Superlist Productivity Platform', category: 'SaaS Product Website', imageUrl: '/websites/superlist-productivity-min.png', liveUrl: 'https://www.superlist.com/', description: 'A powerful workspace for notes, tasks, and project planning designed to enhance team collaboration and personal productivity across multiple platforms.' },
  { id: 'linear-dev-tools', title: 'Linear – Dev Management Tools', category: 'Developer Tools Website', imageUrl: '/websites/linear-dev-tools-min.png', liveUrl: 'https://linear.app/', description: 'Streamlined product planning for agile software teams with intuitive issue tracking, roadmaps, and workflow automation to accelerate development cycles.' },
  { id: 'pitch-presentation', title: 'Pitch – Presentation Software', category: 'Creative Software Website', imageUrl: '/websites/pitch-presentation-min.png', liveUrl: 'https://pitch.com/', description: 'Beautiful, collaborative presentations made fast and simple with real-time editing, custom templates, and seamless team collaboration features.' },
];

interface PortfolioCardProps {
  item: PortfolioItemType;
  delay: number;
  onImageClick: (imageUrl: string) => void;
}

const PortfolioCard: React.FC<PortfolioCardProps> = ({ item, delay, onImageClick }) => {
  return (
    <ScrollReveal delay={delay * 100} direction="down">
      <div className="group relative overflow-hidden rounded-2xl luxury-card hover:shadow-neon-glow transition-all duration-500 h-full flex flex-col hover:translate-y-[-4px] hover:border-neon-cyan">
        {/* Enhanced premium glow effects */}
        <div className="absolute -inset-2 rounded-3xl bg-gradient-aurora opacity-0 group-hover:opacity-40 blur-xl z-0 transition-opacity duration-500"></div>
        <div className="absolute -inset-1 rounded-2xl bg-gradient-luxury opacity-0 group-hover:opacity-20 blur-lg z-0 transition-opacity duration-500"></div>

        {/* Premium border frame */}
        <div className="absolute -inset-0.5 rounded-2xl bg-gradient-premium opacity-0 group-hover:opacity-60 z-0 transition-opacity duration-500"></div>

        <DialogTrigger asChild>
          <div
            className="relative w-full aspect-[16/9] overflow-hidden cursor-pointer z-10"
            onClick={() => onImageClick(item.imageUrl)}
          >
            <OptimizedImage
              src={item.imageUrl}
              alt={`${item.title} - Professional ${item.category} design by Econic Media featuring responsive layout and conversion-optimized user experience`}
              className="w-full h-full transition-transform duration-700 group-hover:scale-105"
              objectFit="cover"
              loading="lazy"
              priority={delay < 3} // Prioritize first 3 images
            />
            {/* Enhanced gradient overlay for image */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/10 group-hover:from-black/20 transition-all duration-500"></div>
          </div>
        </DialogTrigger>

        <div className="p-5 md:p-6 flex-grow flex flex-col relative z-10">
          <h3 className="text-lg md:text-xl font-semibold mb-1 text-white group-hover:text-gradient-aurora transition-all duration-300">{item.title}</h3>
          <p className="text-xs md:text-sm text-neon-cyan mb-3">{item.category}{item.location ? ` - ${item.location}` : ''}</p>
          <p className="text-sm text-foreground/70 mb-4 flex-grow text-ellipsis overflow-hidden line-clamp-2 group-hover:text-foreground/80 transition-colors duration-300">
            {item.description}
          </p>

          <Button
            asChild
            size="sm"
            className="mt-auto w-full premium-button text-sm"
          >
            <a href={item.liveUrl} target="_blank" rel="noopener noreferrer">
              View Live <ArrowUpRight size={16} className="ml-2" />
            </a>
          </Button>
        </div>

        {/* Premium accent elements - Static */}
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-premium-gold rounded-full opacity-0 group-hover:opacity-80 z-20 transition-opacity duration-500"></div>
        <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-premium-silver rounded-full opacity-0 group-hover:opacity-60 z-20 transition-opacity duration-500"></div>
      </div>
    </ScrollReveal>
  );
};

const PortfolioSection: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  return (
    <Dialog open={!!selectedImage} onOpenChange={(isOpen) => !isOpen && setSelectedImage(null)}>
      <section id="websites" className="section-padding relative overflow-hidden">
        {/* Enhanced premium background layers */}
        <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
        <div className="absolute inset-0 bg-gradient-radial from-neon-purple/10 via-transparent to-neon-cyan/5"></div>
        <div className="absolute inset-0 bg-gradient-aurora opacity-5"></div>

        {/* Enhanced gradient orb decorations - Static */}
        <div className="absolute top-20 right-1/4 w-64 h-64 bg-gradient-ocean opacity-30 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-1/4 w-80 h-80 bg-gradient-sunset opacity-25 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 w-96 h-96 bg-gradient-aurora opacity-10 rounded-full blur-3xl"></div>

        {/* Premium floating particles - Static */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-premium-gold rounded-full opacity-60"></div>
        <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-premium-silver rounded-full opacity-40"></div>
        <div className="absolute top-1/3 right-1/4 w-1.5 h-1.5 bg-premium-platinum rounded-full opacity-50"></div>



        <div className="container max-w-7xl mx-auto relative z-10">
          <div className="text-center">
            <ScrollReveal>
              <div className="inline-block mb-4 px-6 py-2 rounded-full premium-glass text-sm font-medium">
                <span className="text-gradient-aurora">🌐 Website Portfolio</span>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={200}>
              <h2 className="text-4xl md:text-5xl font-black text-gradient-aurora drop-shadow-glow mb-12" style={{fontWeight: '950', WebkitTextStroke: '0.5px currentColor'}}>
                Websites We've Built
              </h2>
            </ScrollReveal>

            <ScrollReveal delay={400}>
              <p className="text-lg text-foreground/80 mb-16 max-w-3xl mx-auto">
                From creative freelancers to corporate firms, explore some of our favorite projects crafted for performance and elegance.
              </p>
            </ScrollReveal>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {portfolioItemsData.map((item, index) => (
              <PortfolioCard key={item.id} item={item} delay={index} onImageClick={setSelectedImage} />
            ))}
          </div>
        </div>
      </section>
      {selectedImage && (
        <DialogContent className="max-w-3xl max-h-[80vh] p-0 bg-transparent border-none flex items-center justify-center">
          <OptimizedImage
            src={selectedImage}
            alt="Detailed view of website design by Econic Media showing responsive layout and UI elements"
            className="max-w-full max-h-full rounded-lg"
            objectFit="contain"
            priority={true}
          />
        </DialogContent>
      )}
    </Dialog>
  );
};

export default PortfolioSection;
